{"compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "out", "lib": ["ES2020", "DOM"], "sourceMap": true, "rootDir": "src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "jsx": "react-jsx", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true}, "exclude": ["node_modules", ".vscode-test"]}