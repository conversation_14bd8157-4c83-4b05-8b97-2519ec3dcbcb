import * as vscode from 'vscode';

export class MicrochipZephyrProvider implements vscode.TreeDataProvider<ZephyrQuickLinkItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<ZephyrQuickLinkItem | undefined | null | void> = new vscode.EventEmitter<ZephyrQuickLinkItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<ZephyrQuickLinkItem | undefined | null | void> = this._onDidChangeTreeData.event;

    constructor() {}

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    getTreeItem(element: ZephyrQuickLinkItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: ZephyrQuickLinkItem): Thenable<ZephyrQuickLinkItem[]> {
        if (!element) {
            // Root level items
            return Promise.resolve([
                new ZephyrQuickLinkItem(
                    'Clock Manager',
                    'Open the Microchip Zephyr Clock Manager panel',
                    vscode.TreeItemCollapsibleState.None,
                    {
                        command: 'microchipZephyr.openPanel',
                        title: 'Clock Manager',
                        arguments: []
                    }
                ),
                new ZephyrQuickLinkItem(
                    'AI Assistant',
                    'Open the AI Assistant chat panel',
                    vscode.TreeItemCollapsibleState.None,
                    {
                        command: 'microchipZephyr.openAIChat',
                        title: 'AI Assistant',
                        arguments: []
                    }
                )
            ]);
        }
        return Promise.resolve([]);
    }
}

export class ZephyrQuickLinkItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly tooltip: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly command?: vscode.Command
    ) {
        super(label, collapsibleState);
        this.tooltip = tooltip;
        this.description = '';

        // Add appropriate icon based on label
        if (label === 'AI Assistant') {
            this.iconPath = new vscode.ThemeIcon('robot');
        } else {
            this.iconPath = new vscode.ThemeIcon('clock');
        }
    }
}
