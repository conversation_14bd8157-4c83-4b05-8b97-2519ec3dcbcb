{"name": "microchip-zephyr", "displayName": "Microchip Zephyr Support Extention", "description": "A VSCode extension for Zephyr development with Microchip devices.", "version": "0.0.1", "repository": {"type": "git", "url": "https://github.com/microchip-technology/vscode-zephyr-extension.git"}, "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "clock-configurator-container", "title": "Microchip Zephyr", "icon": "src/resource/mchp-icon.png"}]}, "views": {"clock-configurator-container": [{"id": "clockConfiguratorView", "name": "Clock Manager", "icon": "src/resource/mchp-icon.png"}], "explorer": [{"id": "microchipZephyrSupportView", "name": "Microchip Zephyr Support", "icon": "src/resource/mchp-icon.png"}]}, "commands": [{"command": "microchipZephyr.openPanel", "title": "Clock Manager", "icon": "$(clock)"}, {"command": "microchipZephyr.showHelloWorld", "title": "Microchip Zephyr Support", "category": "Microchip Zephyr"}, {"command": "microchipZephyr.openAIChat", "title": "Open AI Assistant", "category": "Microchip Zephyr", "icon": "$(robot)"}, {"command": "microchipZephyr.askAI", "title": "Ask AI Assistant", "category": "Microchip Zephyr", "icon": "$(comment-discussion)"}], "configuration": {"title": "Microchip Zephyr AI Assistant", "properties": {"microchipZephyr.llm.apiUrl": {"type": "string", "default": "https://api.microchip.com/llm/v1/complete", "description": "API URL for the Microchip LLM service"}, "microchipZephyr.llm.accessKey": {"type": "string", "default": "", "description": "Access key for the Microchip LLM service"}, "microchipZephyr.llm.promptCachingDisabled": {"type": "boolean", "default": false, "description": "Disable prompt caching for the LLM service"}, "microchipZephyr.agent.maxIterations": {"type": "number", "default": 10, "description": "Maximum number of iterations for the AI agent"}, "microchipZephyr.agent.maxExecutionTime": {"type": "number", "default": 60000, "description": "Maximum execution time for the AI agent (in milliseconds)"}, "microchipZephyr.agent.verbose": {"type": "boolean", "default": false, "description": "Enable verbose logging for the AI agent"}}}, "menus": {"view/title": [{"command": "microchipZephyr.openPanel", "when": "view == clockConfiguratorView", "group": "navigation"}], "view/item/context": [{"command": "microchipZephyr.openPanel", "when": "view == clockConfiguratorView", "group": "inline"}], "commandPalette": [{"command": "microchipZephyr.openPanel"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "webpack --mode production", "watch": "webpack --mode development --watch", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/node": "16.x", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.9", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "css-loader": "^7.1.2", "eslint": "^8.28.0", "style-loader": "^4.0.0", "ts-loader": "^9.4.1", "typescript": "^4.9.4", "webpack": "^5.75.0", "webpack-cli": "^5.0.1"}, "dependencies": {"@langchain/community": "^0.3.47", "@langchain/core": "^0.3.61", "langchain": "^0.3.29", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primereact": "^10.9.6", "react": "^18.2.0", "react-dom": "^18.2.0"}}