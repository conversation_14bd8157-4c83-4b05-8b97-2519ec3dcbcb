import React, { useState, useEffect, useRef } from 'react';

interface ChatMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: number;
    isStreaming?: boolean;
    intermediateSteps?: Array<{
        action: string;
        observation: string;
    }>;
}

interface ChatComponentProps {
    messages: ChatMessage[];
    onSendMessage: (message: string) => void;
    onClearChat: () => void;
    onExportChat: () => void;
}

const ChatComponent: React.FC<ChatComponentProps> = ({
    messages,
    onSendMessage,
    onClearChat,
    onExportChat
}) => {
    const [inputValue, setInputValue] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLTextAreaElement>(null);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);

    const handleSend = () => {
        if (inputValue.trim() && !isLoading) {
            setIsLoading(true);
            onSendMessage(inputValue.trim());
            setInputValue('');
            setTimeout(() => setIsLoading(false), 1000);
        }
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSend();
        }
    };

    const formatTimestamp = (timestamp: number) => {
        return new Date(timestamp).toLocaleTimeString();
    };

    const renderMessage = (message: ChatMessage) => {
        const isUser = message.role === 'user';
        
        return (
            <div key={message.id} className={`message ${isUser ? 'user-message' : 'assistant-message'}`}>
                <div className="message-header">
                    <span className="message-role">
                        {isUser ? '👤 You' : '🤖 AI Assistant'}
                    </span>
                    <span className="message-time">
                        {formatTimestamp(message.timestamp)}
                    </span>
                </div>
                <div className="message-content">
                    {message.isStreaming ? (
                        <div className="streaming-indicator">
                            <span>Thinking</span>
                            <div className="dots">
                                <span>.</span>
                                <span>.</span>
                                <span>.</span>
                            </div>
                        </div>
                    ) : (
                        <div className="message-text">
                            {message.content}
                        </div>
                    )}
                    
                    {message.intermediateSteps && message.intermediateSteps.length > 0 && (
                        <details className="intermediate-steps">
                            <summary>View reasoning steps ({message.intermediateSteps.length})</summary>
                            <div className="steps-content">
                                {message.intermediateSteps.map((step, index) => (
                                    <div key={index} className="step">
                                        <div className="step-action">
                                            <strong>Action:</strong> {step.action}
                                        </div>
                                        <div className="step-observation">
                                            <strong>Result:</strong> {step.observation}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </details>
                    )}
                </div>
            </div>
        );
    };

    return (
        <div className="chat-container">
            <div className="chat-header">
                <h2>🤖 AI Assistant</h2>
                <div className="chat-controls">
                    <button 
                        onClick={onClearChat} 
                        className="control-button"
                        title="Clear Chat"
                    >
                        🗑️
                    </button>
                    <button 
                        onClick={onExportChat} 
                        className="control-button"
                        title="Export Chat"
                    >
                        📄
                    </button>
                </div>
            </div>
            
            <div className="chat-messages">
                {messages.length === 0 ? (
                    <div className="empty-state">
                        <div className="empty-icon">💬</div>
                        <h3>Welcome to AI Assistant</h3>
                        <p>I can help you with:</p>
                        <ul>
                            <li>📁 File operations (read, write, search)</li>
                            <li>⚙️ Configuration management</li>
                            <li>📋 Task creation and management</li>
                            <li>🌐 HTTP requests and API calls</li>
                            <li>💻 Running terminal commands</li>
                            <li>🔍 Code search and analysis</li>
                        </ul>
                        <p>Just type your request below to get started!</p>
                    </div>
                ) : (
                    messages.map(renderMessage)
                )}
                <div ref={messagesEndRef} />
            </div>
            
            <div className="chat-input-container">
                <div className="input-wrapper">
                    <textarea
                        ref={inputRef}
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder="Type your message here... (Press Enter to send, Shift+Enter for new line)"
                        className="chat-input"
                        rows={3}
                        disabled={isLoading}
                    />
                    <button
                        onClick={handleSend}
                        disabled={!inputValue.trim() || isLoading}
                        className={`send-button ${isLoading ? 'loading' : ''}`}
                    >
                        {isLoading ? '⏳' : '📤'}
                    </button>
                </div>
                {inputValue.length > 0 && (
                    <div className="input-hint">
                        Press Enter to send • Shift+Enter for new line
                    </div>
                )}
            </div>
        </div>
    );
};

export default ChatComponent;
