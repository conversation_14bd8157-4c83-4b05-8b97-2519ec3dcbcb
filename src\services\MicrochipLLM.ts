import { LLM, BaseLLMParams } from "@langchain/core/language_models/llms";
import { CallbackManagerForLLMRun } from "@langchain/core/callbacks/manager";
import { GenerationChunk } from "@langchain/core/outputs";
import { MicrochipLLMService } from './MicrochipLLMService';

export interface MicrochipLLMParams extends BaseLLMParams {
    apiUrl?: string;
    accessKey?: string;
    promptCachingDisabled?: boolean;
    temperature?: number;
    maxTokens?: number;
    streaming?: boolean;
}

/**
 * Custom LangChain LLM wrapper for Microchip's private LLM service
 * This class implements the LangChain LLM interface to work with the company's internal LLM
 */
export class MicrochipLLM extends LLM {
    private llmService: MicrochipLLMService;
    private temperature: number;
    private maxTokens: number;
    private streaming: boolean;

    static lc_name() {
        return "MicrochipLLM";
    }

    constructor(params: MicrochipLLMParams = {}) {
        super(params);
        
        this.temperature = params.temperature ?? 0.7;
        this.maxTokens = params.maxTokens ?? 4096;
        this.streaming = params.streaming ?? false;

        // Initialize the LLM service with the provided options
        const llmOptions = {
            apiUrl: params.apiUrl,
            accessKey: params.accessKey,
            promptCachingDisabled: params.promptCachingDisabled
        };

        this.llmService = new MicrochipLLMService(llmOptions);
    }

    /**
     * Get the identifying parameters for this LLM
     */
    get identifyingParams() {
        return {
            model_name: "microchip-llm",
            temperature: this.temperature,
            max_tokens: this.maxTokens,
            streaming: this.streaming,
        };
    }

    /**
     * Return the string type key uniquely identifying this class of LLM
     */
    _llmType(): string {
        return "microchip-llm";
    }

    /**
     * Main method for calling the LLM with a prompt
     * This is the core method that LangChain agents will use
     */
    async _call(
        prompt: string,
        options?: this["ParsedCallOptions"],
        runManager?: CallbackManagerForLLMRun
    ): Promise<string> {
        try {
            let result: string;

            if (this.streaming && runManager) {
                // Handle streaming response
                result = await this.llmService.streamPrompt(prompt, (token: string) => {
                    // Send each token to the callback manager for streaming
                    runManager.handleLLMNewToken(token);
                });
            } else {
                // Handle regular (non-streaming) response
                result = await this.llmService.completePrompt(prompt);
            }

            return result;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Streaming implementation for the LLM
     * This allows for real-time token generation in chat interfaces
     */
    async *_streamResponseChunks(
        prompt: string,
        options?: this["ParsedCallOptions"],
        runManager?: CallbackManagerForLLMRun
    ): AsyncGenerator<GenerationChunk> {
        try {
            let fullResponse = "";

            await this.llmService.streamPrompt(prompt, (token: string) => {
                fullResponse += token;
                const chunk = new GenerationChunk({
                    text: token,
                    generationInfo: {
                        finishReason: null,
                        logprobs: null
                    }
                });

                // Yield the chunk for streaming
                return chunk;
            });

            // Yield final chunk with completion info
            const finalChunk = new GenerationChunk({
                text: "",
                generationInfo: {
                    finishReason: "stop",
                    logprobs: null
                }
            });

            yield finalChunk;

        } catch (error) {
            throw error;
        }
    }

    /**
     * Update the LLM service configuration
     */
    updateConfig(newOptions: any): void {
        this.llmService.updateOptions(newOptions);
    }

    /**
     * Clear the response cache
     */
    clearCache(): void {
        this.llmService.clearCache();
    }

    /**
     * Get the current configuration
     */
    getConfig() {
        return {
            temperature: this.temperature,
            maxTokens: this.maxTokens,
            streaming: this.streaming,
            llmType: this._llmType(),
            identifyingParams: this.identifyingParams
        };
    }

    /**
     * Create a new instance with updated parameters
     */
    withConfig(params: Partial<MicrochipLLMParams>): MicrochipLLM {
        return new MicrochipLLM({
            ...this.identifyingParams,
            ...params
        });
    }

}
