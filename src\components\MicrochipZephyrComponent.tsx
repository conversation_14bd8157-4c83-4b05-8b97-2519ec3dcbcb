import React, { useState, useEffect } from 'react';
import { But<PERSON> } from 'primereact/button';
import { Dropdown } from 'primereact/dropdown';
import { Card } from 'primereact/card';
import "primereact/resources/themes/saga-blue/theme.css";
import "primereact/resources/primereact.min.css";
import "primeicons/primeicons.css";
import "primeflex/primeflex.css";

// In a real extension, this would come from the VS Code API
const getAvailableProjects = () => {
    return [
        { label: 'Project A', value: 'project-a' },
        { label: 'Project B', value: 'project-b' },
        { label: 'Browse...', value: 'browse' },
    ];
};

const SvgPlaceholder: React.FC<{ configuratorName: string }> = ({ configuratorName }) => (
    <div style={{ border: '2px dashed #ccc', padding: '20px', margin: '20px 0', textAlign: 'center' }}>
        <svg width="100" height="100">
            <rect width="100" height="100" fill="#f0f0f0" />
            <text x="50" y="50" textAnchor="middle" alignmentBaseline="middle" fontSize="12">
                {configuratorName}
            </text>
        </svg>
    </div>
);


const MicrochipZephyrComponent: React.FC = () => {
    const [selectedProject, setSelectedProject] = useState<string | null>(null);
    const [availableProjects, setAvailableProjects] = useState<any[]>([]);
    const [selectedConfigurator, setSelectedConfigurator] = useState<string | null>(null);

    useEffect(() => {
        const projects = getAvailableProjects();
        setAvailableProjects(projects);
        if (projects.length === 1 && projects[0].value !== 'browse') {
            setSelectedProject(projects[0].value);
        }
    }, []);

    const handleProjectSelection = (e: any) => {
        if (e.value === 'browse') {
            // In a real extension, you would use the VS Code API to open a file dialog
            // and then load the selected project.
            alert("Browsing for a project...");
            // For demonstration, let's add a new project to the list
            const newProject = { label: `Project C`, value: `project-c` };
            setAvailableProjects([...availableProjects, newProject]);
            setSelectedProject(newProject.value);
        } else {
            setSelectedProject(e.value);
        }
    };

    const handleLaunchConfigurator = () => {
        if (selectedConfigurator) {
            // Send message to the extension to launch the configurator
            // @ts-ignore
            vscode.postMessage({ command: 'launchConfigurator', configuratorName: selectedConfigurator });
        }
    };

    const configuratorOptions = [
        { label: 'Clock Configurator', value: 'Clock Configurator' },
        { label: 'Pin Configurator', value: 'Pin Configurator' },
        { label: 'ADC Configurator', value: 'ADC Configurator' },
        { label: 'Interrupt Configurator', value: 'Interrupt Configurator' },
    ];

    return (
        <div className="p-d-flex p-jc-center p-ai-center" style={{ minHeight: '100vh', background: '#f4f4f4' }}>
            <Card title="Microchip Zephyr Control Panel" style={{ width: '600px' }}>
                <div className="p-fluid">

                    <div className="p-field p-grid p-ai-center">
                        <div className="p-col-6 p-d-flex p-ai-center">
                            <label htmlFor="project-selector" style={{ marginRight: '0.5rem' }}>Zephyr Project</label>
                        </div>
                        <div className="p-col-6">
                            <Dropdown
                                id="project-selector"
                                value={selectedProject}
                                options={availableProjects}
                                onChange={handleProjectSelection}
                                placeholder="Select a Project"
                            />
                            {selectedProject && <p style={{ marginTop: '10px' }}>Selected Project: <strong>{selectedProject}</strong></p>}
                        </div>
                    </div>

                    <div className="p-field p-grid p-ai-center" style={{ marginTop: '2rem' }}>
                        <div className="p-col-12">
                            <div className="p-d-flex p-ai-center p-jc-between">
                                <div className="p-d-flex p-ai-center">
                                    <label htmlFor="configurator-selector" style={{ marginRight: '0.5rem' }}>Select Configurator</label>
                                    <Dropdown
                                        id="configurator-selector"
                                        value={selectedConfigurator}
                                        options={configuratorOptions}
                                        onChange={(e) => setSelectedConfigurator(e.value)}
                                        placeholder="Select a Configurator"
                                    />
                                </div>
                                <Button
                                    label="Launch"
                                    icon="pi pi-external-link"
                                    onClick={handleLaunchConfigurator}
                                    disabled={!selectedConfigurator || !selectedProject}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </Card>
        </div>
    );
};

export default MicrochipZephyrComponent;
