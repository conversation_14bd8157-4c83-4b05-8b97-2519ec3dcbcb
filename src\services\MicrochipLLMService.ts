import * as vscode from 'vscode';

interface ModelInfo {
    supportsPromptCache: boolean;
    maxTokens?: number;
    contextWindow?: number;
}

interface ModelConfig {
    id: string;
    info: ModelInfo;
}

interface LLMOptions {
    promptCachingDisabled?: boolean;
    apiUrl?: string;
    accessKey?: string;
}

interface CacheEntry<T> {
    value: T;
    timestamp: number;
    ttl: number;
}

class ResponseCache {
    private cache = new Map<string, CacheEntry<any>>();
    private readonly defaultTTL = 1000 * 60 * 30; // 30 minutes

    set<T>(key: string, value: T, ttl?: number): void {
        this.cache.set(key, {
            value,
            timestamp: Date.now(),
            ttl: ttl || this.defaultTTL
        });
    }

    get<T>(key: string): T | null {
        const entry = this.cache.get(key);
        if (!entry) return null;

        if (Date.now() - entry.timestamp > entry.ttl) {
            this.cache.delete(key);
            return null;
        }

        return entry.value;
    }

    has(key: string): boolean {
        return this.get(key) !== null;
    }

    clear(): void {
        this.cache.clear();
    }
}

export class MicrochipLLMService {
    private responseCache = new ResponseCache();
    private readonly defaultModel: ModelConfig = {
        id: 'microchip-llm-v1',
        info: {
            supportsPromptCache: true,
            maxTokens: 4096,
            contextWindow: 8192
        }
    };

    constructor(private options: LLMOptions = {}) {
        // Set default API URL if not provided
        if (!this.options.apiUrl) {
            this.options.apiUrl = this.getConfigValue('microchipZephyr.llm.apiUrl', 'https://api.microchip.com/llm/v1/complete');
        }
        
        // Set default access key if not provided
        if (!this.options.accessKey) {
            this.options.accessKey = this.getConfigValue('microchipZephyr.llm.accessKey', '');
        }
    }

    private getConfigValue(key: string, defaultValue: string): string {
        return vscode.workspace.getConfiguration().get(key, defaultValue);
    }

    private get apiUrl(): string {
        return this.options.apiUrl || 'https://api.microchip.com/llm/v1/complete';
    }

    private getModel(): ModelConfig {
        return this.defaultModel;
    }

    private getHeaders(): Record<string, string> {
        const headers: Record<string, string> = {
            'Content-Type': 'application/json'
        };

        if (this.options.accessKey) {
            headers['Authorization'] = `Bearer ${this.options.accessKey}`;
        }

        return headers;
    }

    private generateCacheKey(systemPrompt: string, messages: Array<{role: string, content: string}>, modelId: string): string {
        const content = JSON.stringify({ systemPrompt, messages, modelId });
        // Simple hash function for cache key
        let hash = 0;
        for (let i = 0; i < content.length; i++) {
            const char = content.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return `llm_${Math.abs(hash)}`;
    }

    private ensurePromptStartsWithImStart(prompt: string): string {
        // Add any prompt preprocessing logic here
        return prompt.trim();
    }

    private injectAgentPrompt(prompt: string): string {
        const agentPrompt = `You are a helpful AI developer assistant integrated into VSCode using a private LLM. You can access tools like Jira integration, file editing, and config updates. Your job is to break down the user's intent, invoke the right tools when needed, and return useful output.

User Request: ${prompt}`;
        return agentPrompt;
    }

    async completePrompt(prompt: string): Promise<string> {
        const { id: modelId, info } = this.getModel();

        try {
            const cacheKey = this.generateCacheKey("", [{ role: "user", content: prompt }], modelId);
            const useCache = info.supportsPromptCache && !this.options.promptCachingDisabled;

            if (useCache && this.responseCache.has(cacheKey)) {
                const cached = this.responseCache.get<string>(cacheKey);
                if (cached) {
                    console.log("Using cached response for Microchip API completePrompt", {
                        ctx: "microchip",
                        model: modelId,
                        cacheHit: true,
                    });
                    return cached;
                }
            }

            const processedPrompt = this.injectAgentPrompt(this.ensurePromptStartsWithImStart(prompt));
            const payload = { questions: [processedPrompt] };

            const response = await fetch(this.apiUrl, {
                method: "POST",
                headers: this.getHeaders(),
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error(`Microchip API error. Status: ${response.status}`);
            }

            const result = await response.text();

            if (!result) {
                console.warn("Empty response from Microchip API", { ctx: "microchip", model: modelId });
                return "I'm sorry, I couldn't generate a response. Please try again later.";
            }

            if (useCache) {
                this.responseCache.set(cacheKey, result);
            }

            return result;
        } catch (error: any) {
            console.error("Microchip API error in completePrompt", {
                ctx: "microchip",
                error: error instanceof Error ? error.message : String(error)
            });
            throw new Error("There was an issue contacting the server 😓 Please try again later.");
        }
    }

    async streamPrompt(prompt: string, onToken?: (token: string) => void): Promise<string> {
        // For now, fallback to regular completion
        // In the future, this could be enhanced to support streaming
        const result = await this.completePrompt(prompt);
        if (onToken) {
            // Simulate streaming by sending the result in chunks
            const words = result.split(' ');
            for (const word of words) {
                onToken(word + ' ');
                await new Promise(resolve => setTimeout(resolve, 50));
            }
        }
        return result;
    }

    clearCache(): void {
        this.responseCache.clear();
    }

    updateOptions(newOptions: Partial<LLMOptions>): void {
        this.options = { ...this.options, ...newOptions };
    }
}
