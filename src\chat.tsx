import React from 'react';
import { createRoot } from 'react-dom/client';
import ChatComponent from './components/ChatComponent';

// VS Code API
declare const vscode: any;

interface ChatMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: number;
    isStreaming?: boolean;
    intermediateSteps?: Array<{
        action: string;
        observation: string;
    }>;
}

const ChatApp: React.FC = () => {
    const [messages, setMessages] = React.useState<ChatMessage[]>([]);
    const [currentSettings, setCurrentSettings] = React.useState<{accessKey: string; apiUrl: string}>({
        accessKey: '',
        apiUrl: ''
    });

    React.useEffect(() => {
        // Request current settings when component mounts
        vscode.postMessage({
            command: 'getSettings'
        });

        // Listen for messages from the extension
        const messageHandler = (event: MessageEvent) => {
            const message = event.data;

            switch (message.type) {
                case 'addMessage':
                    setMessages(prev => [...prev, message.message]);
                    break;

                case 'updateMessage':
                    setMessages(prev =>
                        prev.map(msg =>
                            msg.id === message.message.id ? message.message : msg
                        )
                    );
                    break;

                case 'clearMessages':
                    setMessages([]);
                    break;

                case 'currentSettings':
                    setCurrentSettings(message.settings);
                    break;

                case 'settingsUpdated':
                    if (message.success) {
                        // Settings were updated successfully
                        console.log('Settings updated successfully');
                    }
                    break;
            }
        };

        window.addEventListener('message', messageHandler);

        return () => {
            window.removeEventListener('message', messageHandler);
        };
    }, []);

    const handleSendMessage = (text: string) => {
        vscode.postMessage({
            command: 'sendMessage',
            text: text
        });
    };

    const handleClearChat = () => {
        vscode.postMessage({
            command: 'clearChat'
        });
    };

    const handleExportChat = () => {
        vscode.postMessage({
            command: 'exportChat'
        });
    };

    const handleUpdateSettings = (settings: { accessKey: string; apiUrl: string }) => {
        vscode.postMessage({
            command: 'updateSettings',
            settings: settings
        });
        setCurrentSettings(settings);
    };

    return (
        <ChatComponent
            messages={messages}
            onSendMessage={handleSendMessage}
            onClearChat={handleClearChat}
            onExportChat={handleExportChat}
            onUpdateSettings={handleUpdateSettings}
        />
    );
};

// Initialize the React app
const container = document.getElementById('root');
if (container) {
    const root = createRoot(container);
    root.render(<ChatApp />);
}
