import React from 'react';
import { createRoot } from 'react-dom/client';
import ChatComponent from './components/ChatComponent';

// VS Code API
declare const vscode: any;

interface ChatMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: number;
    isStreaming?: boolean;
    intermediateSteps?: Array<{
        action: string;
        observation: string;
    }>;
}

const ChatApp: React.FC = () => {
    const [messages, setMessages] = React.useState<ChatMessage[]>([]);

    React.useEffect(() => {
        // Listen for messages from the extension
        const messageHandler = (event: MessageEvent) => {
            const message = event.data;
            
            switch (message.command) {
                case 'addMessage':
                    setMessages(prev => [...prev, message.message]);
                    break;
                    
                case 'updateMessage':
                    setMessages(prev => 
                        prev.map(msg => 
                            msg.id === message.message.id ? message.message : msg
                        )
                    );
                    break;
                    
                case 'clearMessages':
                    setMessages([]);
                    break;
            }
        };

        window.addEventListener('message', messageHandler);
        
        return () => {
            window.removeEventListener('message', messageHandler);
        };
    }, []);

    const handleSendMessage = (text: string) => {
        vscode.postMessage({
            command: 'sendMessage',
            text: text
        });
    };

    const handleClearChat = () => {
        vscode.postMessage({
            command: 'clearChat'
        });
    };

    const handleExportChat = () => {
        vscode.postMessage({
            command: 'exportChat'
        });
    };

    return (
        <ChatComponent
            messages={messages}
            onSendMessage={handleSendMessage}
            onClearChat={handleClearChat}
            onExportChat={handleExportChat}
        />
    );
};

// Initialize the React app
const container = document.getElementById('root');
if (container) {
    const root = createRoot(container);
    root.render(<ChatApp />);
}
