import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { Tool } from "@langchain/core/tools";

/**
 * Tool for reading file contents
 */
export class ReadFileTool extends Tool {
    name = "ReadFile";
    description = "Reads the contents of a file. Input should be the file path relative to workspace root.";

    async _call(filePath: string): Promise<string> {
        try {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspaceRoot) {
                return "Error: No workspace folder is open";
            }

            const fullPath = path.resolve(workspaceRoot, filePath);
            
            // Security check: ensure the path is within workspace
            if (!fullPath.startsWith(workspaceRoot)) {
                return "Error: File path must be within workspace";
            }

            const content = await fs.promises.readFile(fullPath, 'utf8');
            return `File content of ${filePath}:\n${content}`;
        } catch (error) {
            return `Error reading file: ${error instanceof Error ? error.message : String(error)}`;
        }
    }
}

/**
 * Tool for writing file contents
 */
export class WriteFileTool extends Tool {
    name = "WriteFile";
    description = "Writes content to a file. Input should be JSON with 'filePath' and 'content' properties.";

    async _call(input: string): Promise<string> {
        try {
            const { filePath, content } = JSON.parse(input);
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            
            if (!workspaceRoot) {
                return "Error: No workspace folder is open";
            }

            const fullPath = path.resolve(workspaceRoot, filePath);
            
            // Security check: ensure the path is within workspace
            if (!fullPath.startsWith(workspaceRoot)) {
                return "Error: File path must be within workspace";
            }

            // Ensure directory exists
            const dir = path.dirname(fullPath);
            await fs.promises.mkdir(dir, { recursive: true });

            await fs.promises.writeFile(fullPath, content, 'utf8');
            return `Successfully wrote content to ${filePath}`;
        } catch (error) {
            return `Error writing file: ${error instanceof Error ? error.message : String(error)}`;
        }
    }
}

/**
 * Tool for listing directory contents
 */
export class ListDirectoryTool extends Tool {
    name = "ListDirectory";
    description = "Lists the contents of a directory. Input should be the directory path relative to workspace root.";

    async _call(dirPath: string): Promise<string> {
        try {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspaceRoot) {
                return "Error: No workspace folder is open";
            }

            const fullPath = path.resolve(workspaceRoot, dirPath || '.');
            
            // Security check: ensure the path is within workspace
            if (!fullPath.startsWith(workspaceRoot)) {
                return "Error: Directory path must be within workspace";
            }

            const items = await fs.promises.readdir(fullPath, { withFileTypes: true });
            const fileList = items.map(item => {
                const type = item.isDirectory() ? 'DIR' : 'FILE';
                return `${type}: ${item.name}`;
            }).join('\n');

            return `Contents of ${dirPath || 'workspace root'}:\n${fileList}`;
        } catch (error) {
            return `Error listing directory: ${error instanceof Error ? error.message : String(error)}`;
        }
    }
}

/**
 * Tool for editing configuration files
 */
export class EditConfigTool extends Tool {
    name = "EditConfig";
    description = "Edits VSCode configuration. Input should be JSON with 'key' and 'value' properties.";

    async _call(input: string): Promise<string> {
        try {
            const { key, value } = JSON.parse(input);
            const config = vscode.workspace.getConfiguration();
            
            await config.update(key, value, vscode.ConfigurationTarget.Workspace);
            return `Successfully updated configuration: ${key} = ${JSON.stringify(value)}`;
        } catch (error) {
            return `Error updating configuration: ${error instanceof Error ? error.message : String(error)}`;
        }
    }
}

/**
 * Tool for creating Jira tasks (mock implementation)
 */
export class CreateJiraTaskTool extends Tool {
    name = "CreateJiraTask";
    description = "Creates a Jira task. Input should be JSON with 'title' and 'description' properties.";

    async _call(input: string): Promise<string> {
        try {
            const { title, description } = JSON.parse(input);
            
            // Mock implementation - in real scenario, this would call Jira API
            const taskId = `TASK-${Math.floor(Math.random() * 10000)}`;
            
            // Show notification to user
            vscode.window.showInformationMessage(`Jira task created: ${taskId} - ${title}`);
            
            return `Successfully created Jira task ${taskId}:\nTitle: ${title}\nDescription: ${description}`;
        } catch (error) {
            return `Error creating Jira task: ${error instanceof Error ? error.message : String(error)}`;
        }
    }
}

/**
 * Tool for making HTTP requests
 */
export class HttpRequestTool extends Tool {
    name = "HttpRequest";
    description = "Makes an HTTP request. Input should be JSON with 'url', 'method', and optional 'body' and 'headers' properties.";

    async _call(input: string): Promise<string> {
        try {
            const { url, method = 'GET', body, headers = {} } = JSON.parse(input);
            
            const requestOptions: RequestInit = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    ...headers
                }
            };

            if (body && method !== 'GET') {
                requestOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
            }

            const response = await fetch(url, requestOptions);
            const responseText = await response.text();
            
            return `HTTP ${method} ${url}\nStatus: ${response.status} ${response.statusText}\nResponse: ${responseText}`;
        } catch (error) {
            return `Error making HTTP request: ${error instanceof Error ? error.message : String(error)}`;
        }
    }
}

/**
 * Tool for running terminal commands
 */
export class RunCommandTool extends Tool {
    name = "RunCommand";
    description = "Runs a terminal command in the workspace. Input should be the command string.";

    async _call(command: string): Promise<string> {
        try {
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
            if (!workspaceRoot) {
                return "Error: No workspace folder is open";
            }

            // Create a new terminal
            const terminal = vscode.window.createTerminal({
                name: 'Agent Command',
                cwd: workspaceRoot
            });

            terminal.sendText(command);
            terminal.show();

            return `Command executed in terminal: ${command}`;
        } catch (error) {
            return `Error running command: ${error instanceof Error ? error.message : String(error)}`;
        }
    }
}

/**
 * Tool for searching files
 */
export class SearchFilesTool extends Tool {
    name = "SearchFiles";
    description = "Searches for files within the workspace by pattern. Input should be JSON with optional 'filePattern' property (e.g., '**/*.ts' for TypeScript files).";

    async _call(input: string): Promise<string> {
        try {
            const { query, filePattern = "**/*" } = JSON.parse(input);

            // Use workspace.findFiles instead of findTextInFiles (which doesn't exist)
            const files = await vscode.workspace.findFiles(filePattern, null, 50);

            if (files.length === 0) {
                return `No files found matching pattern: ${filePattern}`;
            }

            let resultText = `Found ${files.length} files matching "${filePattern}":\n`;
            for (const file of files) {
                const relativePath = vscode.workspace.asRelativePath(file);
                resultText += `  ${relativePath}\n`;
            }

            return resultText;
        } catch (error) {
            return `Error searching files: ${error instanceof Error ? error.message : String(error)}`;
        }
    }
}

/**
 * Get all available tools for the agent
 */
export function getAllTools(): Tool[] {
    return [
        new ReadFileTool(),
        new WriteFileTool(),
        new ListDirectoryTool(),
        new EditConfigTool(),
        new CreateJiraTaskTool(),
        new HttpRequestTool(),
        new RunCommandTool(),
        new SearchFilesTool()
    ];
}
