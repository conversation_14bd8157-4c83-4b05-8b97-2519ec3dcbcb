import * as vscode from 'vscode';
import { MicrochipZephyrPanel } from './MicrochipZephyrPanel';
import { MicrochipZephyrProvider } from './MicrochipZephyrProvider';
import { MicrochipZephyrSupportProvider } from './MicrochipZephyrSupportProvider';
import { AIChatPanel } from './panels/AIChatPanel';

export function activate(context: vscode.ExtensionContext) {
    console.log('Microchip Zephyr extension is now active!');    // Register the TreeDataProvider for the Clock Configurator view
    const microchipZephyrProvider = new MicrochipZephyrProvider();
    vscode.window.registerTreeDataProvider('clockConfiguratorView', microchipZephyrProvider);

    // Register the TreeDataProvider for the Microchip Zephyr Support view
    const microchipZephyrSupportProvider = new MicrochipZephyrSupportProvider();
    vscode.window.registerTreeDataProvider('microchipZephyrSupportView', microchipZephyrSupportProvider);    // Open panel when view container becomes visible
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('clock-configurator-container', {
            resolveWebviewView: () => {
                MicrochipZephyrPanel.createOrShow(context.extensionUri);
            }
        })
    );

    // Register the command to open the Clock Configurator panel
    const openPanelCommand = vscode.commands.registerCommand('microchipZephyr.openPanel', () => {
        MicrochipZephyrPanel.createOrShow(context.extensionUri);
    });

    // Register the command to show hello world
    const showHelloWorldCommand = vscode.commands.registerCommand('microchipZephyr.showHelloWorld', () => {
        MicrochipZephyrPanel.createOrShow(context.extensionUri);
    });

    // Register the command to launch a new configurator panel
    const launchConfiguratorCommand = vscode.commands.registerCommand('microchipZephyr.launchConfigurator', (configuratorName: string) => {
        MicrochipZephyrPanel.createOrShow(context.extensionUri, configuratorName);
    });

    // Register the command to open AI Chat
    const openAIChatCommand = vscode.commands.registerCommand('microchipZephyr.openAIChat', () => {
        AIChatPanel.createOrShow(context.extensionUri);
    });

    // Register the command to ask AI assistant
    const askAICommand = vscode.commands.registerCommand('microchipZephyr.askAI', async () => {
        const input = await vscode.window.showInputBox({
            prompt: 'What would you like to ask the AI assistant?',
            placeHolder: 'e.g., Create a new component, help with configuration, etc.'
        });

        if (input) {
            AIChatPanel.createOrShow(context.extensionUri);
            // The panel will handle the input when it's ready
        }
    });

    context.subscriptions.push(openPanelCommand, showHelloWorldCommand, launchConfiguratorCommand, openAIChatCommand, askAICommand);

    // Automatically open the panel on startup
    vscode.commands.executeCommand('microchipZephyr.showHelloWorld');
}

export function deactivate() {
    console.log('Microchip Zephyr extension is now deactivated!');
}
