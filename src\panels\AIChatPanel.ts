import * as vscode from 'vscode';
import { getNonce } from '../utilities/getNonce';
import { MicrochipAgent, AgentResponse } from '../agents/MicrochipAgent';

export interface ChatMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: number;
    isStreaming?: boolean;
    intermediateSteps?: Array<{
        action: string;
        observation: string;
    }>;
}

export class AIChatPanel {
    public static currentPanel: AIChatPanel | undefined;
    public static readonly viewType = 'microchipAIChat';

    private readonly _panel: vscode.WebviewPanel;
    private readonly _extensionUri: vscode.Uri;
    private _disposables: vscode.Disposable[] = [];
    private _agent: MicrochipAgent;
    private _messages: ChatMessage[] = [];

    public static createOrShow(extensionUri: vscode.Uri) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        // If we already have a panel, show it.
        if (AIChatPanel.currentPanel) {
            AIChatPanel.currentPanel._panel.reveal(column);
            return;
        }

        // Otherwise, create a new panel.
        const panel = vscode.window.createWebviewPanel(
            AIChatPanel.viewType,
            'AI Assistant',
            column || vscode.ViewColumn.One,
            {
                // Enable javascript in the webview
                enableScripts: true,
                // And restrict the webview to only loading content from our extension's directory.
                localResourceRoots: [
                    vscode.Uri.joinPath(extensionUri, 'media'),
                    vscode.Uri.joinPath(extensionUri, 'out', 'compiled')
                ],
                // Retain context when hidden
                retainContextWhenHidden: true
            }
        );

        AIChatPanel.currentPanel = new AIChatPanel(panel, extensionUri);
    }

    public static kill() {
        AIChatPanel.currentPanel?.dispose();
        AIChatPanel.currentPanel = undefined;
    }

    public static revive(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
        AIChatPanel.currentPanel = new AIChatPanel(panel, extensionUri);
    }

    private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
        this._panel = panel;
        this._extensionUri = extensionUri;
        this._agent = new MicrochipAgent({
            verbose: false,
            maxIterations: 5
        });

        // Initialize the agent
        this._agent.initialize().catch(error => {
            console.error('Failed to initialize AI agent:', error);
            vscode.window.showErrorMessage('Failed to initialize AI agent');
        });

        // Set the webview's initial html content
        this._update();

        // Listen for when the panel is disposed
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

        // Handle messages from the webview
        this._panel.webview.onDidReceiveMessage(
            async message => {
                switch (message.command) {
                    case 'sendMessage':
                        await this._handleUserMessage(message.text);
                        return;
                    case 'clearChat':
                        this._clearChat();
                        return;
                    case 'exportChat':
                        this._exportChat();
                        return;
                }
            },
            null,
            this._disposables
        );

        // Add welcome message
        this._addMessage({
            id: this._generateId(),
            role: 'assistant',
            content: 'Hello! I\'m your AI assistant. I can help you with file operations, configuration changes, creating tasks, and more. What would you like me to help you with?',
            timestamp: Date.now()
        });
    }

    private async _handleUserMessage(text: string): Promise<void> {
        // Add user message
        const userMessage: ChatMessage = {
            id: this._generateId(),
            role: 'user',
            content: text,
            timestamp: Date.now()
        };
        this._addMessage(userMessage);

        // Add streaming assistant message
        const assistantMessage: ChatMessage = {
            id: this._generateId(),
            role: 'assistant',
            content: '',
            timestamp: Date.now(),
            isStreaming: true
        };
        this._addMessage(assistantMessage);

        try {
            // Get response from agent
            const response: AgentResponse = await this._agent.run(text);
            
            // Update the assistant message with the response
            assistantMessage.content = response.output;
            assistantMessage.isStreaming = false;
            assistantMessage.intermediateSteps = response.intermediateSteps;
            
            this._updateMessage(assistantMessage);

        } catch (error) {
            // Update with error message
            assistantMessage.content = `I encountered an error: ${error instanceof Error ? error.message : String(error)}`;
            assistantMessage.isStreaming = false;
            this._updateMessage(assistantMessage);
        }
    }

    private _addMessage(message: ChatMessage): void {
        this._messages.push(message);
        this._panel.webview.postMessage({
            command: 'addMessage',
            message: message
        });
    }

    private _updateMessage(message: ChatMessage): void {
        const index = this._messages.findIndex(m => m.id === message.id);
        if (index !== -1) {
            this._messages[index] = message;
            this._panel.webview.postMessage({
                command: 'updateMessage',
                message: message
            });
        }
    }

    private _clearChat(): void {
        this._messages = [];
        this._panel.webview.postMessage({
            command: 'clearMessages'
        });
        
        // Add welcome message back
        this._addMessage({
            id: this._generateId(),
            role: 'assistant',
            content: 'Chat cleared. How can I help you?',
            timestamp: Date.now()
        });
    }

    private _exportChat(): void {
        const chatContent = this._messages.map(msg => {
            const timestamp = new Date(msg.timestamp).toLocaleString();
            return `[${timestamp}] ${msg.role.toUpperCase()}: ${msg.content}`;
        }).join('\n\n');

        vscode.workspace.openTextDocument({
            content: chatContent,
            language: 'plaintext'
        }).then(doc => {
            vscode.window.showTextDocument(doc);
        });
    }

    private _generateId(): string {
        return Math.random().toString(36).substr(2, 9);
    }

    public dispose() {
        AIChatPanel.currentPanel = undefined;

        // Clean up our resources
        this._panel.dispose();
        this._agent.dispose();

        while (this._disposables.length) {
            const x = this._disposables.pop();
            if (x) {
                x.dispose();
            }
        }
    }

    private _update() {
        const webview = this._panel.webview;
        this._panel.webview.html = this._getHtmlForWebview(webview);
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'out', 'compiled', 'chat.js'));
        const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'chat.css'));
        const nonce = getNonce();

        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';">
                <link href="${styleUri}" rel="stylesheet">
                <title>AI Assistant</title>
            </head>
            <body>
                <div id="chat-container">
                    <div id="chat-header">
                        <h2>AI Assistant</h2>
                        <div id="chat-controls">
                            <button id="clear-chat" title="Clear Chat">🗑️</button>
                            <button id="export-chat" title="Export Chat">📄</button>
                        </div>
                    </div>
                    <div id="chat-messages"></div>
                    <div id="chat-input-container">
                        <textarea id="chat-input" placeholder="Type your message here..." rows="3"></textarea>
                        <button id="send-button">Send</button>
                    </div>
                </div>
                <script nonce="${nonce}" src="${scriptUri}"></script>
            </body>
            </html>`;
    }
}
