import { AgentExecutor, createReactAgent } from "langchain/agents";
import { pull } from "langchain/hub";
import { PromptTemplate } from "@langchain/core/prompts";
import { Tool } from "@langchain/core/tools";
import { MicrochipLLM, MicrochipLLMParams } from '../services/MicrochipLLM';
import { getAllTools } from '../tools/AgentTools';

export interface AgentConfig {
    llmParams?: MicrochipLLMParams;
    tools?: Tool[];
    verbose?: boolean;
    maxIterations?: number;
    maxExecutionTime?: number;
}

export interface AgentResponse {
    output: string;
    intermediateSteps?: Array<{
        action: string;
        observation: string;
    }>;
    error?: string;
}

/**
 * Main agent class that orchestrates the LangChain ReAct agent
 */
export class MicrochipAgent {
    private llm: MicrochipLLM;
    private tools: Tool[];
    private executor: AgentExecutor | null = null;
    private verbose: boolean;
    private maxIterations: number;
    private maxExecutionTime: number;

    constructor(config: AgentConfig = {}) {
        this.llm = new MicrochipLLM(config.llmParams || {});
        this.tools = config.tools || getAllTools();
        this.verbose = config.verbose ?? true;
        this.maxIterations = config.maxIterations ?? 10;
        this.maxExecutionTime = config.maxExecutionTime ?? 60000; // 60 seconds
    }

    /**
     * Initialize the agent executor
     */
    async initialize(): Promise<void> {
        try {
            // Create a custom ReAct prompt template
            const prompt = PromptTemplate.fromTemplate(`
You are a helpful AI developer assistant integrated into VSCode using a private LLM. You can access tools like file operations, Jira integration, config updates, and more. Your job is to break down the user's intent, invoke the right tools when needed, and return useful output.

TOOLS:
------
You have access to the following tools:

{tools}

To use a tool, please use the following format:

\`\`\`
Thought: Do I need to use a tool? Yes
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
\`\`\`

When you have a response to say to the Human, or if you do not need to use a tool, you MUST use the format:

\`\`\`
Thought: Do I need to use a tool? No
Final Answer: [your response here]
\`\`\`

Begin!

Question: {input}
Thought: {agent_scratchpad}
`);

            // Create the ReAct agent
            const agent = await createReactAgent({
                llm: this.llm,
                tools: this.tools,
                prompt: prompt,
            });

            // Create the agent executor
            this.executor = new AgentExecutor({
                agent,
                tools: this.tools,
                verbose: this.verbose,
                maxIterations: this.maxIterations,
                returnIntermediateSteps: true,
            });

        } catch (error) {
            console.error('Failed to initialize agent:', error);
            throw new Error(`Agent initialization failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * Run the agent with a user input
     */
    async run(input: string): Promise<AgentResponse> {
        if (!this.executor) {
            await this.initialize();
        }

        if (!this.executor) {
            throw new Error('Agent executor not initialized');
        }

        try {
            const result = await this.executor.invoke({
                input: input
            });

            return {
                output: result.output,
                intermediateSteps: result.intermediateSteps?.map((step: any) => ({
                    action: step.action?.tool || 'unknown',
                    observation: step.observation || ''
                }))
            };

        } catch (error) {
            console.error('Agent execution error:', error);
            return {
                output: "I encountered an error while processing your request. Please try again.",
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    /**
     * Stream the agent response (for real-time chat)
     */
    async *stream(input: string): AsyncGenerator<string> {
        if (!this.executor) {
            await this.initialize();
        }

        if (!this.executor) {
            throw new Error('Agent executor not initialized');
        }

        try {
            // For now, we'll simulate streaming by yielding the final result
            // In a more advanced implementation, you could hook into the agent's
            // intermediate steps and yield them as they happen
            const result = await this.run(input);
            
            // Simulate streaming by breaking the response into chunks
            const words = result.output.split(' ');
            for (const word of words) {
                yield word + ' ';
                // Small delay to simulate streaming
                await new Promise(resolve => setTimeout(resolve, 50));
            }

        } catch (error) {
            yield `Error: ${error instanceof Error ? error.message : String(error)}`;
        }
    }

    /**
     * Add a custom tool to the agent
     */
    addTool(tool: Tool): void {
        this.tools.push(tool);
        // Reset executor to reinitialize with new tools
        this.executor = null;
    }

    /**
     * Remove a tool from the agent
     */
    removeTool(toolName: string): void {
        this.tools = this.tools.filter(tool => tool.name !== toolName);
        // Reset executor to reinitialize without the removed tool
        this.executor = null;
    }

    /**
     * Get list of available tools
     */
    getAvailableTools(): string[] {
        return this.tools.map(tool => tool.name);
    }

    /**
     * Update LLM configuration
     */
    updateLLMConfig(params: Partial<MicrochipLLMParams>): void {
        this.llm.updateConfig(params);
    }

    /**
     * Clear LLM cache
     */
    clearCache(): void {
        this.llm.clearCache();
    }

    /**
     * Get agent configuration
     */
    getConfig() {
        return {
            llmConfig: this.llm.getConfig(),
            tools: this.tools.map(tool => ({
                name: tool.name,
                description: tool.description
            })),
            verbose: this.verbose,
            maxIterations: this.maxIterations,
            maxExecutionTime: this.maxExecutionTime
        };
    }

    /**
     * Update LLM settings
     */
    updateLLMSettings(settings: { accessKey: string; apiUrl: string }): void {
        // Create new LLM instance with updated settings
        this.llm = new MicrochipLLM({
            accessKey: settings.accessKey,
            apiUrl: settings.apiUrl
        });

        // Reinitialize the agent with new LLM
        this.initialize().catch(error => {
            console.error('Failed to reinitialize agent with new settings:', error);
        });
    }

    /**
     * Dispose of the agent and clean up resources
     */
    dispose(): void {
        this.executor = null;
        this.tools = [];
    }
}
