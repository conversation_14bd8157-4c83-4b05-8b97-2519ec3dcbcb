/* Chat Container */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
    font-family: var(--vscode-font-family);
    font-size: var(--vscode-font-size);
}

/* Chat Header */
.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--vscode-panel-border);
    background-color: var(--vscode-panel-background);
}

.chat-header h2 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.chat-controls {
    display: flex;
    gap: 8px;
}

.control-button {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    border-radius: 4px;
    padding: 6px 8px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.control-button:hover {
    background: var(--vscode-button-hoverBackground);
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--vscode-scrollbarSlider-background);
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-hoverBackground);
    border-radius: 4px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--vscode-descriptionForeground);
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.empty-state h3 {
    margin: 0 0 16px 0;
    color: var(--vscode-editor-foreground);
}

.empty-state ul {
    text-align: left;
    display: inline-block;
    margin: 16px 0;
}

.empty-state li {
    margin: 8px 0;
}

/* Message Styles */
.message {
    max-width: 80%;
    margin-bottom: 16px;
}

.user-message {
    align-self: flex-end;
    margin-left: auto;
}

.assistant-message {
    align-self: flex-start;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
}

.message-role {
    font-weight: 600;
}

.message-time {
    opacity: 0.7;
}

.message-content {
    background: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: 8px;
    padding: 12px;
    position: relative;
}

.user-message .message-content {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.message-text {
    white-space: pre-wrap;
    word-wrap: break-word;
    line-height: 1.5;
}

/* Streaming Indicator */
.streaming-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--vscode-descriptionForeground);
    font-style: italic;
}

.dots {
    display: flex;
    gap: 2px;
}

.dots span {
    animation: blink 1.4s infinite both;
}

.dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes blink {
    0%, 80%, 100% {
        opacity: 0;
    }
    40% {
        opacity: 1;
    }
}

/* Intermediate Steps */
.intermediate-steps {
    margin-top: 12px;
    border-top: 1px solid var(--vscode-panel-border);
    padding-top: 12px;
}

.intermediate-steps summary {
    cursor: pointer;
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    margin-bottom: 8px;
}

.intermediate-steps summary:hover {
    color: var(--vscode-editor-foreground);
}

.steps-content {
    margin-top: 8px;
}

.step {
    background: var(--vscode-editor-background);
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
    font-size: 12px;
}

.step-action {
    color: var(--vscode-symbolIcon-functionForeground);
    margin-bottom: 4px;
}

.step-observation {
    color: var(--vscode-descriptionForeground);
}

/* Chat Input */
.chat-input-container {
    border-top: 1px solid var(--vscode-panel-border);
    padding: 16px;
    background-color: var(--vscode-panel-background);
}

.input-wrapper {
    display: flex;
    gap: 8px;
    align-items: flex-end;
}

.chat-input {
    flex: 1;
    background: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border: 1px solid var(--vscode-input-border);
    border-radius: 6px;
    padding: 12px;
    font-family: inherit;
    font-size: inherit;
    resize: none;
    min-height: 60px;
    max-height: 120px;
}

.chat-input:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
}

.chat-input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.send-button {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    border-radius: 6px;
    padding: 12px 16px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s;
    min-width: 50px;
    height: 48px;
}

.send-button:hover:not(:disabled) {
    background: var(--vscode-button-hoverBackground);
}

.send-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.send-button.loading {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.input-hint {
    font-size: 11px;
    color: var(--vscode-descriptionForeground);
    margin-top: 6px;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 600px) {
    .message {
        max-width: 95%;
    }
    
    .chat-header {
        padding: 8px 12px;
    }
    
    .chat-messages {
        padding: 12px;
    }
    
    .chat-input-container {
        padding: 12px;
    }
}

/* Settings Modal Styles */
.settings-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.settings-modal {
    background-color: var(--vscode-editor-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--vscode-panel-border);
    background-color: var(--vscode-titleBar-activeBackground);
}

.settings-header h3 {
    margin: 0;
    color: var(--vscode-titleBar-activeForeground);
    font-size: 16px;
    font-weight: 600;
}

.close-button {
    background: none;
    border: none;
    color: var(--vscode-titleBar-activeForeground);
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.close-button:hover {
    background-color: var(--vscode-titleBar-inactiveBackground);
}

.settings-content {
    padding: 20px;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--vscode-foreground);
    font-weight: 500;
    font-size: 14px;
}

.setting-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--vscode-input-border);
    border-radius: 4px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    font-size: 14px;
    font-family: var(--vscode-font-family);
    transition: border-color 0.2s;
    box-sizing: border-box;
}

.setting-input:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
    box-shadow: 0 0 0 1px var(--vscode-focusBorder);
}

.setting-input::placeholder {
    color: var(--vscode-input-placeholderForeground);
}

.settings-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid var(--vscode-panel-border);
    background-color: var(--vscode-editor-background);
}

.cancel-button,
.save-button {
    padding: 8px 16px;
    border: 1px solid var(--vscode-button-border);
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    font-family: var(--vscode-font-family);
}

.cancel-button {
    background-color: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
}

.cancel-button:hover {
    background-color: var(--vscode-button-secondaryHoverBackground);
}

.save-button {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.save-button:hover {
    background-color: var(--vscode-button-hoverBackground);
}
